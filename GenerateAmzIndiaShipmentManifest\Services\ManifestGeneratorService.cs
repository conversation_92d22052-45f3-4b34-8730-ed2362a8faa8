using GenerateAmzIndiaShipmentManifest.Models;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GenerateAmzIndiaShipmentManifest.Services
{
    /// <summary>
    /// Service for generating Excel manifests from product data
    /// </summary>
    public class ManifestGeneratorService
    {
        private readonly string _defaultFCValue = "ISK3";
        private readonly uint _startingRowIndex = 10;

        /// <summary>
        /// Generates a manifest Excel file from the provided product data
        /// </summary>
        /// <param name="templatePath">Path to the template Excel file</param>
        /// <param name="outputPath">Path where the output file should be saved</param>
        /// <param name="products">List of enriched product data</param>
        /// <param name="progressCallback">Optional callback for progress updates</param>
        public void GenerateManifest(string templatePath, string outputPath, 
            List<EnrichedProductData> products, Action<string> progressCallback = null)
        {
            if (products == null || products.Count == 0)
            {
                throw new InvalidOperationException("No data to generate manifest");
            }

            try
            {
                // First, make a copy of the template file
                File.Copy(templatePath, outputPath, true);
                LogProgress(progressCallback, "Template file copied successfully");

                // Use OpenXML to modify the Excel file
                using (SpreadsheetDocument spreadsheet = SpreadsheetDocument.Open(outputPath, true))
                {
                    // Get the workbook part
                    WorkbookPart workbookPart = spreadsheet.WorkbookPart ??
                        throw new InvalidOperationException("Could not access workbook part");

                    // Get the workbook
                    Workbook workbook = workbookPart.Workbook;

                    // Get all sheets
                    Sheets sheets = workbook.Sheets ??
                        throw new InvalidOperationException("Could not access sheets");

                    // Find the target sheet
                    Sheet targetSheet = FindTargetSheet(sheets);
                    
                    if (targetSheet == null || string.IsNullOrEmpty(targetSheet.Id?.Value))
                    {
                        throw new InvalidOperationException("Could not find any worksheet in the template file");
                    }

                    // Get the worksheet part
                    WorksheetPart worksheetPart = (WorksheetPart)workbookPart.GetPartById(targetSheet.Id.Value);
                    Worksheet worksheet = worksheetPart.Worksheet;

                    // Get the sheet data which contains all the cells
                    SheetData sheetData = worksheet.GetFirstChild<SheetData>() ??
                        throw new InvalidOperationException("Could not access sheet data");

                    // Get shared string table for storing text values
                    SharedStringTablePart sharedStringPart = GetOrCreateSharedStringPart(workbookPart);

                    // Clear existing data rows
                    ClearExistingRows(sheetData, _startingRowIndex);
                    LogProgress(progressCallback, "Cleared existing data rows");

                    // Add product data to the worksheet
                    AddProductsToWorksheet(sheetData, products, sharedStringPart);
                    LogProgress(progressCallback, "Added product data to worksheet");

                    // Save the changes to the worksheet
                    worksheet.Save();
                    
                    // Save the workbook
                    workbookPart.Workbook.Save();
                    LogProgress(progressCallback, "Manifest file saved successfully");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Error generating manifest: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Finds the target sheet in the Excel workbook
        /// </summary>
        private Sheet FindTargetSheet(Sheets sheets)
        {
            // Find the "Create workflow – template" sheet
            Sheet targetSheet = null;
            
            // First try exact name match
            foreach (Sheet sheet in sheets.Elements<Sheet>())
            {
                if (sheet.Name?.Value?.Equals("Create workflow – template", StringComparison.OrdinalIgnoreCase) == true)
                {
                    targetSheet = sheet;
                    break;
                }
            }

            // If exact name not found, try to find a sheet with similar name
            if (targetSheet == null)
            {
                foreach (Sheet sheet in sheets.Elements<Sheet>())
                {
                    string sheetName = sheet.Name?.Value ?? string.Empty;
                    if (sheetName.Contains("Create workflow", StringComparison.OrdinalIgnoreCase) ||
                        sheetName.Contains("template", StringComparison.OrdinalIgnoreCase))
                    {
                        targetSheet = sheet;
                        break;
                    }
                }
            }

            // If still not found, use the first sheet
            targetSheet ??= sheets.Elements<Sheet>().FirstOrDefault();
            
            return targetSheet;
        }

        /// <summary>
        /// Gets or creates the shared string part for the workbook
        /// </summary>
        private SharedStringTablePart GetOrCreateSharedStringPart(WorkbookPart workbookPart)
        {
            SharedStringTablePart sharedStringPart = workbookPart.GetPartsOfType<SharedStringTablePart>().FirstOrDefault();
            if (sharedStringPart == null)
            {
                // Create a SharedStringTablePart if it doesn't exist
                sharedStringPart = workbookPart.AddNewPart<SharedStringTablePart>();
                sharedStringPart.SharedStringTable = new SharedStringTable();
            }
            return sharedStringPart;
        }

        /// <summary>
        /// Clears existing rows from the starting row index
        /// </summary>
        private void ClearExistingRows(SheetData sheetData, uint startingRowIndex)
        {
            var rowsToRemove = new List<Row>();
            foreach (Row existingRow in sheetData.Elements<Row>().ToList())
            {
                if (existingRow.RowIndex != null && existingRow.RowIndex.Value >= startingRowIndex)
                {
                    rowsToRemove.Add(existingRow);
                }
            }

            // Remove the rows outside the foreach to avoid collection modification issues
            foreach (Row rowToRemove in rowsToRemove)
            {
                sheetData.RemoveChild(rowToRemove);
            }
        }

        /// <summary>
        /// Adds product data to the worksheet
        /// </summary>
        private void AddProductsToWorksheet(SheetData sheetData, List<EnrichedProductData> products, 
            SharedStringTablePart sharedStringPart)
        {
            uint currentRowIndex = _startingRowIndex;

            // Add each product to the worksheet
            foreach (var product in products)
            {
                // Skip products without SKU
                if (string.IsNullOrWhiteSpace(product.SKU))
                {
                    continue;
                }

                // Create a new row
                Row row = new Row { RowIndex = currentRowIndex };

                // Column A: SKU - Create cell for SKU (column 1)
                Cell skuCell = CreateCellWithText(GetExcelColumnName(1) + currentRowIndex, product.SKU, sharedStringPart);
                row.AppendChild(skuCell);

                // Column B: Quantity - Create cell for Quantity (column 2)
                Cell quantityCell = CreateCellWithValue(GetExcelColumnName(2) + currentRowIndex, product.Quantity.ToString());
                row.AppendChild(quantityCell);

                // Column C: FC - Create cell for FC (column 3)
                Cell fcCell = CreateCellWithText(GetExcelColumnName(3) + currentRowIndex, _defaultFCValue, sharedStringPart);
                row.AppendChild(fcCell);

                // Add the row to the sheet
                sheetData.AppendChild(row);

                // Increment row index
                currentRowIndex++;
            }
        }

        /// <summary>
        /// Creates a cell with text value using shared strings
        /// </summary>
        private Cell CreateCellWithText(string cellReference, string text, SharedStringTablePart sharedStringPart)
        {
            // Get the SharedStringTable
            SharedStringTable sharedStringTable = sharedStringPart.SharedStringTable;

            // Check if the string already exists in the SharedStringTable
            int index = 0;
            bool found = false;

            foreach (SharedStringItem item in sharedStringTable.Elements<SharedStringItem>())
            {
                if (item.InnerText == text)
                {
                    found = true;
                    break;
                }
                index++;
            }

            // If the string doesn't exist, add it
            if (!found)
            {
                sharedStringTable.AppendChild(new SharedStringItem(new Text(text)));
                sharedStringTable.Count = (sharedStringTable.Count == null) ? 1 : sharedStringTable.Count + 1;
                sharedStringTable.UniqueCount = (sharedStringTable.UniqueCount == null) ? 1 : sharedStringTable.UniqueCount + 1;
            }

            // Create the cell with the shared string index
            Cell cell = new Cell
            {
                CellReference = cellReference,
                DataType = CellValues.SharedString,
                CellValue = new CellValue(index.ToString())
            };

            return cell;
        }

        /// <summary>
        /// Creates a cell with a numeric value
        /// </summary>
        private Cell CreateCellWithValue(string cellReference, string value)
        {
            Cell cell = new Cell
            {
                CellReference = cellReference,
                DataType = CellValues.Number,
                CellValue = new CellValue(value)
            };

            return cell;
        }

        /// <summary>
        /// Converts a column number to Excel column name (A, B, C, ... AA, AB, etc.)
        /// </summary>
        private string GetExcelColumnName(int columnIndex)
        {
            int dividend = columnIndex;
            string columnName = string.Empty;

            while (dividend > 0)
            {
                int modulo = (dividend - 1) % 26;
                columnName = Convert.ToChar('A' + modulo) + columnName;
                dividend = (dividend - modulo) / 26;
            }

            return columnName;
        }

        /// <summary>
        /// Logs progress if a callback is provided
        /// </summary>
        private void LogProgress(Action<string> progressCallback, string message)
        {
            progressCallback?.Invoke(message);
        }
    }
}
