using System;
using System.IO;
using System.Text;
using System.Windows.Forms;

namespace GenerateAmzIndiaShipmentManifest.Services
{
    /// <summary>
    /// Service for handling errors consistently throughout the application
    /// </summary>
    public class ErrorHandlingService
    {
        private readonly string _logFilePath;
        private readonly bool _enableLogging;
        private readonly bool _enableConsoleOutput;

        /// <summary>
        /// Initializes a new instance of the ErrorHandlingService
        /// </summary>
        /// <param name="logFilePath">Path to the log file</param>
        /// <param name="enableLogging">Whether to enable file logging</param>
        /// <param name="enableConsoleOutput">Whether to output errors to the console</param>
        public ErrorHandlingService(string logFilePath = null, bool enableLogging = true, bool enableConsoleOutput = true)
        {
            _enableLogging = enableLogging;
            _enableConsoleOutput = enableConsoleOutput;
            
            // If no log file path is provided, use the default location
            _logFilePath = logFilePath ?? Path.Combine(
                AppDomain.CurrentDomain.BaseDirectory, "logs", "error.log");
            
            // Ensure the log directory exists
            if (_enableLogging)
            {
                string logDirectory = Path.GetDirectoryName(_logFilePath);
                if (!string.IsNullOrEmpty(logDirectory) && !Directory.Exists(logDirectory))
                {
                    Directory.CreateDirectory(logDirectory);
                }
            }
        }

        /// <summary>
        /// Handles an exception with optional UI feedback
        /// </summary>
        /// <param name="ex">The exception to handle</param>
        /// <param name="context">Context where the exception occurred</param>
        /// <param name="updateStatus">Optional callback to update status in the UI</param>
        /// <param name="showMessageBox">Whether to show a message box to the user</param>
        public void HandleException(Exception ex, string context, Action<string> updateStatus = null, bool showMessageBox = true)
        {
            string errorMessage = $"Error in {context}: {ex.Message}";
            
            // Log the error
            LogError(ex, context);
            
            // Update status if callback provided
            updateStatus?.Invoke(errorMessage);
            
            // Show message box if requested
            if (showMessageBox)
            {
                MessageBox.Show(errorMessage, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Logs an error to the log file and/or console
        /// </summary>
        /// <param name="ex">The exception to log</param>
        /// <param name="context">Context where the exception occurred</param>
        private void LogError(Exception ex, string context)
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] Error in {context}");
            sb.AppendLine($"Message: {ex.Message}");
            
            if (ex.InnerException != null)
            {
                sb.AppendLine($"Inner Exception: {ex.InnerException.Message}");
            }
            
            sb.AppendLine($"Stack Trace: {ex.StackTrace}");
            sb.AppendLine(new string('-', 80));
            
            string logEntry = sb.ToString();
            
            // Log to console if enabled
            if (_enableConsoleOutput)
            {
                Console.WriteLine(logEntry);
            }
            
            // Log to file if enabled
            if (_enableLogging)
            {
                try
                {
                    File.AppendAllText(_logFilePath, logEntry);
                }
                catch
                {
                    // If we can't write to the log file, at least try to output to console
                    if (!_enableConsoleOutput)
                    {
                        Console.WriteLine(logEntry);
                        Console.WriteLine("Failed to write to log file.");
                    }
                }
            }
        }

        /// <summary>
        /// Gets detailed error information for display or logging
        /// </summary>
        /// <param name="ex">The exception to get details for</param>
        /// <returns>Formatted error details</returns>
        public string GetDetailedErrorMessage(Exception ex)
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendLine($"Error: {ex.Message}");
            
            if (ex.InnerException != null)
            {
                sb.AppendLine($"Inner Exception: {ex.InnerException.Message}");
            }
            
            return sb.ToString();
        }
    }
}
