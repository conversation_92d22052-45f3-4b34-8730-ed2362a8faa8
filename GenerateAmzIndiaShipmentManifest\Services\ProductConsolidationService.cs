using GenerateAmzIndiaShipmentManifest.Models;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GenerateAmzIndiaShipmentManifest.Services
{
    /// <summary>
    /// Service for consolidating product data by SKU
    /// </summary>
    public class ProductConsolidationService
    {
        /// <summary>
        /// Consolidates products by SKU, summing quantities for duplicate SKUs
        /// </summary>
        /// <param name="products">List of enriched product data</param>
        /// <returns>Dictionary with SKU as key and total quantity as value</returns>
        public Dictionary<string, int> ConsolidateProducts(List<EnrichedProductData> products)
        {
            if (products == null)
            {
                throw new ArgumentNullException(nameof(products));
            }

            var consolidatedProducts = new Dictionary<string, int>();
            
            // Process each product and consolidate by SKU
            foreach (var product in products)
            {
                // Skip products without SKU
                if (string.IsNullOrWhiteSpace(product.SKU))
                {
                    continue;
                }
                
                // If SKU already exists, add to its quantity, otherwise add new entry
                if (consolidatedProducts.ContainsKey(product.SKU))
                {
                    consolidatedProducts[product.SKU] += product.Quantity;
                }
                else
                {
                    consolidatedProducts[product.SKU] = product.Quantity;
                }
            }
            
            return consolidatedProducts;
        }

        /// <summary>
        /// Creates a list of consolidated product data from the original products
        /// </summary>
        /// <param name="products">Original list of enriched product data</param>
        /// <returns>List of consolidated product data with unique SKUs</returns>
        public List<EnrichedProductData> CreateConsolidatedProductList(List<EnrichedProductData> products)
        {
            var consolidatedDict = ConsolidateProducts(products);
            var result = new List<EnrichedProductData>();
            
            // Group products by SKU to get the first occurrence of each SKU
            var productsBySku = products
                .Where(p => !string.IsNullOrWhiteSpace(p.SKU))
                .GroupBy(p => p.SKU)
                .ToDictionary(g => g.Key, g => g.First());
            
            // Create a new product for each unique SKU with the consolidated quantity
            foreach (var entry in consolidatedDict)
            {
                string sku = entry.Key;
                int totalQuantity = entry.Value;
                
                // If we have a product with this SKU, clone it and update the quantity
                if (productsBySku.TryGetValue(sku, out var originalProduct))
                {
                    var consolidatedProduct = CloneProduct(originalProduct);
                    consolidatedProduct.Quantity = totalQuantity;
                    result.Add(consolidatedProduct);
                }
            }
            
            return result;
        }
        
        /// <summary>
        /// Creates a clone of an EnrichedProductData object
        /// </summary>
        private EnrichedProductData CloneProduct(EnrichedProductData original)
        {
            return new EnrichedProductData
            {
                BoxNo = original.BoxNo,
                ProductName = original.ProductName,
                BarcodeType = original.BarcodeType,
                Quantity = original.Quantity,
                Dimensions = original.Dimensions,
                SKU = original.SKU,
                HSN = original.HSN,
                DeclaredValue = original.DeclaredValue
            };
        }
    }
}
