using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using GenerateAmzIndiaShipmentManifest.Models;

namespace GenerateAmzIndiaShipmentManifest.Services
{
    /// <summary>
    /// Service class for interacting with the OpenAI API
    /// </summary>
    public class OpenAIService
    {
        private readonly HttpClient _client;
        private readonly string _apiKey;
        private readonly ProductNameService _productNameService;

        public OpenAIService(string apiKey, ProductNameService productNameService)
        {
            _apiKey = apiKey;
            _productNameService = productNameService;
            _client = new HttpClient
            {
                BaseAddress = new Uri("https://api.openai.com/v1/")
            };
            _client.DefaultRequestHeaders.Add("Authorization", $"Bearer {_apiKey}");
        }

        /// <summary>
        /// Analyze an image using OpenAI's vision model
        /// </summary>
        /// <param name="imagePath">Path to the image file</param>
        /// <returns>Tuple containing raw JSON and parsed product data</returns>
        public async Task<(string RawJson, List<ProductData>? Products)> AnalyzeImageAsync(string imagePath)
        {
            try
            {
                // Convert image to base64
                byte[] imageBytes = await File.ReadAllBytesAsync(imagePath);
                string base64Image = Convert.ToBase64String(imageBytes);

                // Get product names from the mapping file
                List<string> productNames = _productNameService.GetProductNames();
                string productNamesText = productNames.Count > 0
                    ? $"\n\nAvailable product names from our catalog:\n{string.Join(", ", productNames)}\n\nWhen extracting product names from the image, try to match them with the closest product name from this catalog. If an exact match is not found, use the product name as it appears in the image."
                    : "";

                // Prepare request payload for OpenAI API
                var payload = new
                {
                    model = "gpt-4o",
                    messages = new object[]
                    {
                        new
                        {
                            role = "system",
                            content = $"You are an expert in extracting text from images and converting it into structured data. Extract all text from the image and organize it as a JSON array. The first row is the heading of the tabular data. The image gives information about box details. Each box has its own dimensions. Each box contains single or multiple products. Quantity of each product is mentioned in its respective row. Format the output as a valid JSON array with appropriate field names that exactly match the headers in the image: 'Box No.', 'Product Name', 'Barcode Type', 'Quantity', 'Dimensions'.{productNamesText}"
                        },
                        new
                        {
                            role = "user",
                            content = new object[]
                            {
                                new
                                {
                                    type = "text",
                                    text = $"Extract all text from this image and organize it as a structured JSON array. Pay special attention to product names and try to match them with the provided catalog when possible:{productNamesText}"
                                },
                                new
                                {
                                    type = "image_url",
                                    image_url = new
                                    {
                                        url = $"data:image/jpeg;base64,{base64Image}"
                                    }
                                }
                            }
                        }
                    },
                    max_tokens = 4000
                };

                // Make the API call
                var response = await _client.PostAsJsonAsync("chat/completions", payload);

                // Check if the call was successful
                if (!response.IsSuccessStatusCode)
                {
                    string errorContent = await response.Content.ReadAsStringAsync();

                    string errorMessage = response.StatusCode switch
                    {
                        System.Net.HttpStatusCode.Unauthorized => "Invalid or expired OpenAI API key. Please check your API key in config.json",
                        System.Net.HttpStatusCode.TooManyRequests => "Rate limit exceeded. Please try again later",
                        System.Net.HttpStatusCode.BadRequest => $"Bad request to OpenAI API: {errorContent}",
                        _ => $"API Error: {response.StatusCode}. {errorContent}"
                    };

                    return (
                        RawJson: errorMessage,
                        Products: null
                    );
                }

                // Parse the response
                var responseContent = await response.Content.ReadFromJsonAsync<JsonElement>();
                string resultText = responseContent.GetProperty("choices")[0].GetProperty("message").GetProperty("content").GetString() ?? "No response from API";

                return ParseJsonResponse(resultText);
            }
            catch (Exception ex)
            {
                return (
                    RawJson: $"Error calling OpenAI API: {ex.Message}",
                    Products: null
                );
            }
        }

        /// <summary>
        /// Parse and format the JSON response from OpenAI
        /// </summary>
        /// <param name="resultText">The raw response text</param>
        /// <returns>Tuple containing formatted JSON and parsed product data</returns>
        private (string FormattedJson, List<ProductData>? Products) ParseJsonResponse(string resultText)
        {
            try
            {
                // Extract JSON content from response
                string jsonText = ExtractJsonFromText(resultText);

                // Try to parse the JSON into product data objects
                List<ProductData>? products = null;
                try
                {
                    products = JsonSerializer.Deserialize<List<ProductData>>(
                        jsonText,
                        new JsonSerializerOptions { PropertyNameCaseInsensitive = false }
                    );
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error deserializing JSON: {ex.Message}");
                }

                // Format JSON for display
                string formattedJson = FormatJson(jsonText);

                return (FormattedJson: formattedJson, Products: products);
            }
            catch (Exception)
            {
                // If JSON parsing fails, return the original result
                return (FormattedJson: resultText, Products: null);
            }
        }

        /// <summary>
        /// Extract JSON content from the text response
        /// </summary>
        private string ExtractJsonFromText(string text)
        {
            // Look for JSON array
            if (text.Contains("[") && text.Contains("]"))
            {
                int startIndex = text.IndexOf('[');
                int endIndex = text.LastIndexOf(']') + 1;

                if (startIndex >= 0 && endIndex > startIndex)
                {
                    return text.Substring(startIndex, endIndex - startIndex);
                }
            }

            // Look for JSON object as fallback
            if (text.Contains("{") && text.Contains("}"))
            {
                int startIndex = text.IndexOf('{');
                int endIndex = text.LastIndexOf('}') + 1;

                if (startIndex >= 0 && endIndex > startIndex)
                {
                    return text.Substring(startIndex, endIndex - startIndex);
                }
            }

            return text;
        }

        /// <summary>
        /// Format JSON string with proper indentation
        /// </summary>
        private string FormatJson(string jsonText)
        {
            try
            {
                var jsonDocument = JsonDocument.Parse(jsonText);

                using (var stream = new MemoryStream())
                {
                    using (var writer = new Utf8JsonWriter(stream, new JsonWriterOptions { Indented = true }))
                    {
                        jsonDocument.WriteTo(writer);
                    }

                    return Encoding.UTF8.GetString(stream.ToArray());
                }
            }
            catch
            {
                // If formatting fails, return the original
                return jsonText;
            }
        }
    }
}
