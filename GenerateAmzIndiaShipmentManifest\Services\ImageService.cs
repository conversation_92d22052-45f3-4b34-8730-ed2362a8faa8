using System.Drawing;
using System.Drawing.Imaging;

namespace GenerateAmzIndiaShipmentManifest.Services
{
    /// <summary>
    /// Service class for handling image operations
    /// </summary>
    public class ImageService
    {
        /// <summary>
        /// Loads an image from a file path
        /// </summary>
        /// <param name="imagePath">Path to the image file</param>
        /// <returns>Image object and file information</returns>
        public (Image Image, FileInfo FileInfo) LoadImage(string imagePath)
        {
            if (string.IsNullOrEmpty(imagePath))
            {
                throw new ArgumentException("Image path cannot be empty", nameof(imagePath));
            }

            if (!File.Exists(imagePath))
            {
                throw new FileNotFoundException("Image file not found", imagePath);
            }

            // Load the image from file
            Image image;
            using (var stream = new FileStream(imagePath, FileMode.Open, FileAccess.Read))
            {
                image = Image.FromStream(stream);
            }

            // Get file information
            var fileInfo = new FileInfo(imagePath);

            return (image, fileInfo);
        }

        /// <summary>
        /// Gets a formatted description of the image file
        /// </summary>
        /// <param name="fileInfo">FileInfo object for the image</param>
        /// <returns>Formatted string with file name and size</returns>
        public string GetImageDescription(FileInfo fileInfo)
        {
            if (fileInfo == null)
            {
                return "No image selected";
            }

            return $"{Path.GetFileName(fileInfo.FullName)} ({fileInfo.Length / 1024} KB)";
        }
    }
}
