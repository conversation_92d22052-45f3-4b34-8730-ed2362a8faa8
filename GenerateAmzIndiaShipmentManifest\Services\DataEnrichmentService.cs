using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using GenerateAmzIndiaShipmentManifest.Models;
using System.Text.Json;

namespace GenerateAmzIndiaShipmentManifest.Services
{
    public class DataEnrichmentService
    {
        private readonly string _mappingFilePath;
        private readonly Dictionary<string, MappingData> _mappingCache = new Dictionary<string, MappingData>();

        public DataEnrichmentService(string mappingFilePath)
        {
            _mappingFilePath = mappingFilePath;
            LoadMappingData();
        }

        /// <summary>
        /// Loads mapping data from the Excel file
        /// </summary>
        private void LoadMappingData()
        {
            try
            {
                using (SpreadsheetDocument spreadsheet = SpreadsheetDocument.Open(_mappingFilePath, false))
                {
                    WorkbookPart workbookPart = spreadsheet.WorkbookPart!;
                    Sheet sheet = workbookPart.Workbook.Descendants<Sheet>().First();
                    WorksheetPart worksheetPart = (WorksheetPart)workbookPart.GetPartById(sheet.Id!);
                    SheetData sheetData = worksheetPart.Worksheet.Elements<SheetData>().First();

                    // Get shared string table for workbook
                    SharedStringTablePart stringTablePart = workbookPart.SharedStringTablePart!;
                    SharedStringTable sharedStringTable = stringTablePart.SharedStringTable;

                    // Extract header row to identify column positions
                    Dictionary<string, int> headers = new Dictionary<string, int>();
                    Row? headerRow = sheetData.Elements<Row>().FirstOrDefault();
                    
                    if (headerRow != null)
                    {
                        int colIndex = 0;
                        foreach (Cell cell in headerRow.Elements<Cell>())
                        {
                            string? header = GetCellValue(cell, sharedStringTable);
                            if (!string.IsNullOrEmpty(header))
                            {
                                headers[header] = colIndex;
                            }
                            colIndex++;
                        }
                    }

                    // Process rows after header row
                    foreach (Row row in sheetData.Elements<Row>().Skip(1))
                    {
                        string productName = string.Empty;
                        string barcodeType = string.Empty;
                        string sku = string.Empty;
                        string hsn = string.Empty;
                        decimal declaredValue = 0;

                        // Extract each column value if it exists in the mapping
                        if (headers.ContainsKey("Product Name"))
                        {
                            Cell? cell = GetCellByIndex(row, headers["Product Name"]);
                            productName = cell != null ? GetCellValue(cell, sharedStringTable) : string.Empty;
                        }

                        if (headers.ContainsKey("Barcode Type"))
                        {
                            Cell? cell = GetCellByIndex(row, headers["Barcode Type"]);
                            barcodeType = cell != null ? GetCellValue(cell, sharedStringTable) : string.Empty;
                        }

                        if (headers.ContainsKey("SKU"))
                        {
                            Cell? cell = GetCellByIndex(row, headers["SKU"]);
                            sku = cell != null ? GetCellValue(cell, sharedStringTable) : string.Empty;
                        }

                        if (headers.ContainsKey("HSN"))
                        {
                            Cell? cell = GetCellByIndex(row, headers["HSN"]);
                            hsn = cell != null ? GetCellValue(cell, sharedStringTable) : string.Empty;
                        }

                        if (headers.ContainsKey("Declared Value"))
                        {
                            Cell? cell = GetCellByIndex(row, headers["Declared Value"]);
                            string valueStr = cell != null ? GetCellValue(cell, sharedStringTable) : "0";
                            
                            if (decimal.TryParse(valueStr, out decimal value))
                            {
                                declaredValue = value;
                            }
                        }

                        // Only add valid entries to the mapping cache
                        if (!string.IsNullOrEmpty(productName))
                        {
                            string key = GetMappingKey(productName, barcodeType);
                            _mappingCache[key] = new MappingData
                            {
                                SKU = sku,
                                HSN = hsn,
                                DeclaredValue = declaredValue
                            };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log error or handle exception
                Console.WriteLine($"Error loading mapping data: {ex.Message}");
            }
        }

        /// <summary>
        /// Get cell value from shared string table if needed
        /// </summary>
        private string GetCellValue(Cell cell, SharedStringTable sharedStringTable)
        {
            if (cell.CellValue == null)
                return string.Empty;

            string value = cell.CellValue.Text;

            // If the cell represents a shared string, look up the value in the shared string table
            if (cell.DataType != null && cell.DataType.Value == CellValues.SharedString)
            {
                if (int.TryParse(value, out int ssid))
                {
                    return sharedStringTable.Elements<SharedStringItem>().ElementAt(ssid).InnerText;
                }
            }

            return value;
        }        /// <summary>
        /// Get cell by column index, handling Excel's way of skipping empty cells
        /// </summary>
        private Cell? GetCellByIndex(Row row, int columnIndex)
        {
            // Excel skips empty cells in the XML, so we need to calculate
            // the cell reference based on its column index
            string columnName = GetColumnName(columnIndex);
            string cellReference = $"{columnName}{row.RowIndex}";
            
            // Look for a cell with the expected reference
            Cell? matchingCell = row.Elements<Cell>()
                .FirstOrDefault(c => c.CellReference?.Value == cellReference);
            
            // If found, return it
            if (matchingCell != null)
                return matchingCell;
            
            // If not found, it means the cell is empty
            return null;
        }
        
        /// <summary>
        /// Convert a zero-based column index to an Excel column name (A, B, C, ... AA, AB, etc.)
        /// </summary>
        private string GetColumnName(int columnIndex)
        {
            int dividend = columnIndex + 1; // +1 because Excel columns are 1-based
            string columnName = string.Empty;
            
            while (dividend > 0)
            {
                int modulo = (dividend - 1) % 26;
                columnName = Convert.ToChar('A' + modulo) + columnName;
                dividend = (dividend - modulo) / 26;
            }
            
            return columnName;
        }

        /// <summary>
        /// Create a unique key for the mapping dictionary
        /// </summary>
        private string GetMappingKey(string productName, string barcodeType)
        {
            return $"{productName.Trim().ToLower()} {barcodeType.Trim().ToLower()}";
        }

        /// <summary>
        /// Process JSON data from ChatGPT and enrich it with mapping data
        /// </summary>
        public List<EnrichedProductData> EnrichProductData(string jsonData)
        {
            try
            {
                // Parse the JSON data
                var products = JsonSerializer.Deserialize<List<ProductData>>(jsonData);
                if (products == null)
                    return new List<EnrichedProductData>();

                // Enrich each product with mapping data
                var enrichedProducts = new List<EnrichedProductData>();
                
                foreach (var product in products)
                {
                    var enrichedProduct = EnrichedProductData.FromProductData(product);
                    
                    // Find mapping for this product
                    string key = GetMappingKey(product.ProductName, product.BarcodeType);
                    
                    if (_mappingCache.TryGetValue(key, out var mapping))
                    {
                        enrichedProduct.SKU = mapping.SKU;
                        enrichedProduct.HSN = mapping.HSN;
                        enrichedProduct.DeclaredValue = mapping.DeclaredValue;
                    }
                    
                    enrichedProducts.Add(enrichedProduct);
                }
                
                return enrichedProducts;
            }
            catch (Exception ex)
            {
                // Log error or handle exception
                Console.WriteLine($"Error enriching product data: {ex.Message}");
                return new List<EnrichedProductData>();
            }
        }

        /// <summary>
        /// Helper class for mapping data
        /// </summary>
        private class MappingData
        {
            public string SKU { get; set; } = string.Empty;
            public string HSN { get; set; } = string.Empty;
            public decimal DeclaredValue { get; set; }
        }
    }
}
