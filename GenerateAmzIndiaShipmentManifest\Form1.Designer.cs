namespace GenerateAmzIndiaShipmentManifest
{
    partial class Form1
    {
        /// <summary>
        ///  Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        ///  Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        ///  Required method for Designer support - do not modify
        ///  the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            btnUploadImage = new Button();
            pictureBox = new PictureBox();
            openFileDialog = new OpenFileDialog();
            labelImageInfo = new Label();
            btnAnalyzeImage = new Button();
            textBoxResult = new TextBox();
            dataGridViewProducts = new DataGridView();
            tabPageGrid = new TabPage();
            tabPageJson = new TabPage();
            btnGenerateManifest = new Button();
            labelStatus = new Label();
            dataGridView1 = new DataGridView();
            tabControl = new TabControl();
            ((System.ComponentModel.ISupportInitialize)pictureBox).BeginInit();
            ((System.ComponentModel.ISupportInitialize)dataGridViewProducts).BeginInit();
            tabPageGrid.SuspendLayout();
            tabPageJson.SuspendLayout();
            tabControl.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dataGridView1).BeginInit();
            SuspendLayout();
            //
            // btnUploadImage
            //
            btnUploadImage.Location = new Point(86, 96);
            btnUploadImage.Margin = new Padding(9, 10, 9, 10);
            btnUploadImage.Name = "btnUploadImage";
            btnUploadImage.Size = new Size(429, 96);
            btnUploadImage.TabIndex = 0;
            btnUploadImage.Text = "Upload Image";
            btnUploadImage.UseVisualStyleBackColor = true;
            btnUploadImage.Click += btnUploadImage_Click;
            //
            // pictureBox
            //
            pictureBox.BorderStyle = BorderStyle.FixedSingle;
            pictureBox.Location = new Point(86, 256);
            pictureBox.Margin = new Padding(9, 10, 9, 10);
            pictureBox.Name = "pictureBox";
            pictureBox.Size = new Size(1139, 956);
            pictureBox.SizeMode = PictureBoxSizeMode.Zoom;
            pictureBox.TabIndex = 1;
            pictureBox.TabStop = false;
            //
            // openFileDialog
            //
            openFileDialog.Filter = "Image Files|*.jpg;*.jpeg;*.png;*.gif;*.bmp";
            openFileDialog.Title = "Select an Image";
            //
            // labelImageInfo
            //
            labelImageInfo.AutoSize = true;
            labelImageInfo.Location = new Point(571, 122);
            labelImageInfo.Margin = new Padding(9, 0, 9, 0);
            labelImageInfo.Name = "labelImageInfo";
            labelImageInfo.Size = new Size(317, 48);
            labelImageInfo.TabIndex = 2;
            labelImageInfo.Text = "No image selected";
            //
            // btnAnalyzeImage
            //
            btnAnalyzeImage.Enabled = false;
            btnAnalyzeImage.Location = new Point(86, 1280);
            btnAnalyzeImage.Margin = new Padding(9, 10, 9, 10);
            btnAnalyzeImage.Name = "btnAnalyzeImage";
            btnAnalyzeImage.Size = new Size(429, 96);
            btnAnalyzeImage.TabIndex = 3;
            btnAnalyzeImage.Text = "Analyze with ChatGPT";
            btnAnalyzeImage.UseVisualStyleBackColor = true;
            btnAnalyzeImage.Click += btnAnalyzeImage_Click;
            //
            // textBoxResult
            //
            textBoxResult.Dock = DockStyle.Fill;
            textBoxResult.Location = new Point(3, 3);
            textBoxResult.Multiline = true;
            textBoxResult.Name = "textBoxResult";
            textBoxResult.ReadOnly = true;
            textBoxResult.ScrollBars = ScrollBars.Vertical;
            textBoxResult.Size = new Size(636, 266);
            textBoxResult.TabIndex = 0;
            //
            // dataGridViewProducts
            //
            dataGridViewProducts.AllowUserToAddRows = false;
            dataGridViewProducts.AllowUserToDeleteRows = false;
            dataGridViewProducts.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dataGridViewProducts.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridViewProducts.Dock = DockStyle.Fill;
            dataGridViewProducts.Location = new Point(3, 3);
            dataGridViewProducts.Name = "dataGridViewProducts";
            dataGridViewProducts.ReadOnly = true;
            dataGridViewProducts.RowHeadersWidth = 123;
            dataGridViewProducts.RowTemplate.Height = 25;
            dataGridViewProducts.Size = new Size(636, 266);
            dataGridViewProducts.TabIndex = 0;
            //
            // tabPageGrid
            //
            tabPageGrid.Controls.Add(dataGridViewProducts);
            tabPageGrid.Location = new Point(4, 24);
            tabPageGrid.Name = "tabPageGrid";
            tabPageGrid.Padding = new Padding(3);
            tabPageGrid.Size = new Size(642, 272);
            tabPageGrid.TabIndex = 0;
            tabPageGrid.Text = "Data Table";
            tabPageGrid.UseVisualStyleBackColor = true;
            //
            // tabPageJson
            //
            tabPageJson.Controls.Add(textBoxResult);
            tabPageJson.Location = new Point(4, 24);
            tabPageJson.Name = "tabPageJson";
            tabPageJson.Padding = new Padding(3);
            tabPageJson.Size = new Size(642, 272);
            tabPageJson.TabIndex = 1;
            tabPageJson.Text = "JSON";
            tabPageJson.UseVisualStyleBackColor = true;
            //
            // btnGenerateManifest
            //
            btnGenerateManifest.Enabled = false;
            btnGenerateManifest.Location = new Point(571, 1280);
            btnGenerateManifest.Margin = new Padding(9, 10, 9, 10);
            btnGenerateManifest.Name = "btnGenerateManifest";
            btnGenerateManifest.Size = new Size(429, 96);
            btnGenerateManifest.TabIndex = 5;
            btnGenerateManifest.Text = "Generate Manifest";
            btnGenerateManifest.UseVisualStyleBackColor = true;
            btnGenerateManifest.Click += btnGenerateManifest_Click;
            //
            // labelStatus
            //
            labelStatus.BackColor = SystemColors.Info;
            labelStatus.BorderStyle = BorderStyle.FixedSingle;
            labelStatus.Location = new Point(1057, 1280);
            labelStatus.Margin = new Padding(9, 0, 9, 0);
            labelStatus.Name = "labelStatus";
            labelStatus.Size = new Size(882, 92);
            labelStatus.TabIndex = 7;
            labelStatus.Text = "Ready";
            labelStatus.TextAlign = ContentAlignment.MiddleLeft;
            //
            // dataGridView1
            //
            dataGridView1.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dataGridView1.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
            dataGridView1.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridView1.Location = new Point(86, 1482);
            dataGridView1.Name = "dataGridView1";
            dataGridView1.RowHeadersWidth = 123;
            dataGridView1.Size = new Size(1853, 554);
            dataGridView1.TabIndex = 8;
            //
            // tabControl
            //
            tabControl.Controls.Add(tabPageGrid);
            tabControl.Controls.Add(tabPageJson);
            tabControl.Location = new Point(1300, 256);
            tabControl.Name = "tabControl";
            tabControl.SelectedIndex = 0;
            tabControl.Size = new Size(650, 300);
            tabControl.TabIndex = 9;
            //
            // Form1
            //
            AutoScaleDimensions = new SizeF(20F, 48F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(2000, 2097);
            Controls.Add(dataGridView1);
            Controls.Add(tabControl);
            Controls.Add(btnGenerateManifest);
            Controls.Add(btnAnalyzeImage);
            Controls.Add(labelStatus);
            Controls.Add(labelImageInfo);
            Controls.Add(pictureBox);
            Controls.Add(btnUploadImage);
            Margin = new Padding(9, 10, 9, 10);
            Name = "Form1";
            Text = "Amazon India Shipment Manifest Generator";
            ((System.ComponentModel.ISupportInitialize)pictureBox).EndInit();
            ((System.ComponentModel.ISupportInitialize)dataGridViewProducts).EndInit();
            tabPageGrid.ResumeLayout(false);
            tabPageJson.ResumeLayout(false);
            tabPageJson.PerformLayout();
            tabControl.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)dataGridView1).EndInit();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion
        private Button btnUploadImage;
        private PictureBox pictureBox;
        private OpenFileDialog openFileDialog;
        private Label labelImageInfo;
        private Button btnAnalyzeImage;
        private TextBox textBoxResult;
        private DataGridView dataGridViewProducts;
        private TabPage tabPageGrid;
        private TabPage tabPageJson;
        private Button btnGenerateManifest;
        private Label labelStatus;
        private DataGridView dataGridView1;
        private TabControl tabControl;
    }
}
