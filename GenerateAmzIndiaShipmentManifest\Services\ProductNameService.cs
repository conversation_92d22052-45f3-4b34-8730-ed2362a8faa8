using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace GenerateAmzIndiaShipmentManifest.Services
{
    /// <summary>
    /// Service for reading product names from the Mapping.xlsx file
    /// </summary>
    public class ProductNameService
    {
        private readonly string _mappingFilePath;
        private List<string>? _cachedProductNames;

        public ProductNameService(string mappingFilePath)
        {
            _mappingFilePath = mappingFilePath ?? throw new ArgumentNullException(nameof(mappingFilePath));
        }

        /// <summary>
        /// Gets all product names from the 'Product Name' column in the Mapping.xlsx file
        /// </summary>
        /// <returns>List of product names</returns>
        public List<string> GetProductNames()
        {
            // Return cached result if available
            if (_cachedProductNames != null)
            {
                return _cachedProductNames;
            }

            try
            {
                _cachedProductNames = ReadProductNamesFromExcel();
                return _cachedProductNames;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error reading product names from mapping file: {ex.Message}");
                return new List<string>();
            }
        }

        /// <summary>
        /// Forces a refresh of the cached product names
        /// </summary>
        public void RefreshProductNames()
        {
            _cachedProductNames = null;
            GetProductNames();
        }

        /// <summary>
        /// Reads product names from the Excel file
        /// </summary>
        private List<string> ReadProductNamesFromExcel()
        {
            var productNames = new List<string>();

            if (!File.Exists(_mappingFilePath))
            {
                Console.WriteLine($"Mapping file not found: {_mappingFilePath}");
                return productNames;
            }

            using (SpreadsheetDocument spreadsheet = SpreadsheetDocument.Open(_mappingFilePath, false))
            {
                WorkbookPart workbookPart = spreadsheet.WorkbookPart ??
                    throw new InvalidOperationException("Could not access workbook part");

                // Get the first worksheet
                WorksheetPart worksheetPart = workbookPart.WorksheetParts.FirstOrDefault() ??
                    throw new InvalidOperationException("Could not find any worksheet");

                Worksheet worksheet = worksheetPart.Worksheet;
                SheetData sheetData = worksheet.GetFirstChild<SheetData>() ??
                    throw new InvalidOperationException("Could not access sheet data");

                // Get shared string table for reading text values
                SharedStringTablePart? sharedStringPart = workbookPart.GetPartsOfType<SharedStringTablePart>().FirstOrDefault();
                SharedStringTable? sharedStringTable = sharedStringPart?.SharedStringTable;

                // Find the 'Product Name' column
                int productNameColumnIndex = FindProductNameColumn(sheetData, sharedStringTable);
                
                if (productNameColumnIndex == -1)
                {
                    Console.WriteLine("Could not find 'Product Name' column in the mapping file");
                    return productNames;
                }

                // Read all product names from the column (skip header row)
                var rows = sheetData.Elements<Row>().Skip(1); // Skip header row
                
                foreach (Row row in rows)
                {
                    var cell = GetCellInColumn(row, productNameColumnIndex);
                    if (cell != null)
                    {
                        string productName = GetCellValue(cell, sharedStringTable);
                        if (!string.IsNullOrWhiteSpace(productName))
                        {
                            productNames.Add(productName.Trim());
                        }
                    }
                }
            }

            // Remove duplicates and sort
            productNames = productNames.Distinct().OrderBy(name => name).ToList();
            
            Console.WriteLine($"Found {productNames.Count} unique product names in mapping file");
            return productNames;
        }

        /// <summary>
        /// Finds the column index for 'Product Name' column
        /// </summary>
        private int FindProductNameColumn(SheetData sheetData, SharedStringTable? sharedStringTable)
        {
            // Get the first row (header row)
            Row? headerRow = sheetData.Elements<Row>().FirstOrDefault();
            if (headerRow == null) return -1;

            int columnIndex = 1;
            foreach (Cell cell in headerRow.Elements<Cell>())
            {
                string cellValue = GetCellValue(cell, sharedStringTable);
                if (cellValue.Equals("Product Name", StringComparison.OrdinalIgnoreCase))
                {
                    return columnIndex;
                }
                columnIndex++;
            }

            return -1; // Column not found
        }

        /// <summary>
        /// Gets the cell in a specific column for a given row
        /// </summary>
        private Cell? GetCellInColumn(Row row, int columnIndex)
        {
            string columnName = GetExcelColumnName(columnIndex);
            uint rowIndex = row.RowIndex?.Value ?? 0;
            string cellReference = columnName + rowIndex;

            return row.Elements<Cell>().FirstOrDefault(c => 
                c.CellReference?.Value?.Equals(cellReference, StringComparison.OrdinalIgnoreCase) == true);
        }

        /// <summary>
        /// Gets the value of a cell
        /// </summary>
        private string GetCellValue(Cell cell, SharedStringTable? sharedStringTable)
        {
            if (cell.CellValue == null) return string.Empty;

            string value = cell.CellValue.Text;

            // If the cell contains a shared string, get the actual string value
            if (cell.DataType?.Value == CellValues.SharedString && sharedStringTable != null)
            {
                if (int.TryParse(value, out int index))
                {
                    var sharedStringItem = sharedStringTable.Elements<SharedStringItem>().ElementAtOrDefault(index);
                    return sharedStringItem?.InnerText ?? string.Empty;
                }
            }

            return value;
        }

        /// <summary>
        /// Converts a column number to Excel column name (A, B, C, ... AA, AB, etc.)
        /// </summary>
        private string GetExcelColumnName(int columnIndex)
        {
            int dividend = columnIndex;
            string columnName = string.Empty;

            while (dividend > 0)
            {
                int modulo = (dividend - 1) % 26;
                columnName = Convert.ToChar('A' + modulo) + columnName;
                dividend = (dividend - modulo) / 26;
            }

            return columnName;
        }
    }
}
