using System;
using System.IO;
using System.Text.Json;

namespace GenerateAmzIndiaShipmentManifest.Services
{
    /// <summary>
    /// Service for managing application configuration
    /// </summary>
    public class AppConfigurationService
    {
        private AppConfiguration _configuration;
        private readonly string _configFilePath;

        public AppConfigurationService(string configFilePath = null)
        {
            // If no config file path is provided, use the default location
            _configFilePath = configFilePath ?? Path.Combine(
                AppDomain.CurrentDomain.BaseDirectory, "config.json");

            LoadConfiguration();
        }

        /// <summary>
        /// Gets the current application configuration
        /// </summary>
        public AppConfiguration Configuration => _configuration;

        /// <summary>
        /// Loads the configuration from the config file
        /// </summary>
        private void LoadConfiguration()
        {
            try
            {
                if (File.Exists(_configFilePath))
                {
                    string json = File.ReadAllText(_configFilePath);
                    _configuration = JsonSerializer.Deserialize<AppConfiguration>(json) ?? CreateDefaultConfiguration();
                }
                else
                {
                    _configuration = CreateDefaultConfiguration();
                    SaveConfiguration(); // Create the default config file
                }
            }
            catch (Exception)
            {
                // If there's an error loading the configuration, use defaults
                _configuration = CreateDefaultConfiguration();
            }
        }

        /// <summary>
        /// Saves the current configuration to the config file
        /// </summary>
        public void SaveConfiguration()
        {
            try
            {
                string json = JsonSerializer.Serialize(_configuration, new JsonSerializerOptions
                {
                    WriteIndented = true
                });

                // Ensure the directory exists
                string directory = Path.GetDirectoryName(_configFilePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                File.WriteAllText(_configFilePath, json);
            }
            catch (Exception)
            {
                // Log error or handle exception
            }
        }

        /// <summary>
        /// Creates a default configuration with sensible defaults
        /// </summary>
        private AppConfiguration CreateDefaultConfiguration()
        {
            return new AppConfiguration
            {
                OpenAIApiKey = "***************************************************", // Replace with your actual API key
                DefaultFCValue = "ISK3",
                StartingRowIndex = 10,
                TemplateFileName = "ManifestFileUpload_Template.xlsx"
            };
        }
    }

    /// <summary>
    /// Application configuration class
    /// </summary>
    public class AppConfiguration
    {
        /// <summary>
        /// OpenAI API key for image analysis
        /// </summary>
        public string OpenAIApiKey { get; set; }

        /// <summary>
        /// Default FC value for manifest generation
        /// </summary>
        public string DefaultFCValue { get; set; }

        /// <summary>
        /// Starting row index for data in the manifest
        /// </summary>
        public uint StartingRowIndex { get; set; }

        /// <summary>
        /// Template file name for manifest generation
        /// </summary>
        public string TemplateFileName { get; set; }
    }
}
