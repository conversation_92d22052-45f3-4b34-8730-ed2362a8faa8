using System.Text.Json;
using System.Text.Json.Serialization;

namespace GenerateAmzIndiaShipmentManifest.Models
{
    /// <summary>
    /// Converter that can handle various formats of integers in JSON
    /// </summary>
    public class FlexibleIntConverter : JsonConverter<int>
    {
        public override int Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            if (reader.TokenType == JsonTokenType.Number)
            {
                return reader.GetInt32();
            }
            else if (reader.TokenType == JsonTokenType.String)
            {
                string stringValue = reader.GetString() ?? "0";
                
                // Try to parse the string value as an integer
                if (int.TryParse(stringValue, out int intValue))
                {
                    return intValue;
                }
                
                // Try to parse as decimal and convert to int
                if (decimal.TryParse(stringValue, out decimal decimalValue))
                {
                    return (int)decimalValue;
                }
            }
            
            // Default to 0 if we can't parse
            return 0;
        }

        public override void Write(Utf8JsonWriter writer, int value, JsonSerializerOptions options)
        {
            writer.WriteNumberValue(value);
        }
    }

    /// <summary>
    /// Represents product data extracted from image
    /// </summary>
    public class ProductData
    {        [JsonPropertyName("Box No.")]
        [JsonConverter(typeof(FlexibleIntConverter))]
        public int BoxNo { get; set; }
        
        [JsonPropertyName("Product Name")]
        public string ProductName { get; set; } = string.Empty;
        
        [JsonPropertyName("Barcode Type")]
        public string BarcodeType { get; set; } = string.Empty;
        
        [JsonPropertyName("Quantity")]
        [JsonConverter(typeof(FlexibleIntConverter))]
        public int Quantity { get; set; }
        
        [JsonPropertyName("Dimensions")]
        public string Dimensions { get; set; } = string.Empty;
    }

    /// <summary>
    /// Represents enriched product data with additional mapping information
    /// </summary>
    public class EnrichedProductData : ProductData
    {
        [JsonPropertyName("SKU")]
        public string SKU { get; set; } = string.Empty;
        
        [JsonPropertyName("HSN")]
        public string HSN { get; set; } = string.Empty;
        
        [JsonPropertyName("Declared Value")]
        public decimal DeclaredValue { get; set; }
        
        [JsonPropertyName("Total Value")]
        public decimal TotalValue => Quantity * DeclaredValue;

        // Create an enriched product from a base product
        public static EnrichedProductData FromProductData(ProductData product)
        {
            return new EnrichedProductData
            {
                BoxNo = product.BoxNo,
                ProductName = product.ProductName,
                BarcodeType = product.BarcodeType,
                Quantity = product.Quantity,
                Dimensions = product.Dimensions
            };
        }
    }
}
