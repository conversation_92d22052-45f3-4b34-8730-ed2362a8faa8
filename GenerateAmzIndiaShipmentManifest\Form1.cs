using GenerateAmzIndiaShipmentManifest.Models;
using GenerateAmzIndiaShipmentManifest.Services;
using System.Text.Json;
// Use fully qualified name for Color to avoid ambiguity
using SystemColor = System.Drawing.Color;

namespace GenerateAmzIndiaShipmentManifest
{
    public partial class Form1 : Form
    {
        // Variable to store the uploaded image path
        private string currentImagePath = string.Empty;
        // Store the current enriched data
        private List<EnrichedProductData>? _currentEnrichedProducts;
        // Store the raw ChatGPT JSON output for editing
        private string _currentRawJson = string.Empty;

        // Services
        private readonly OpenAIService _openAIService;
        private readonly DataEnrichmentService _dataEnrichmentService;
        private readonly ImageService _imageService;
        private readonly ManifestGeneratorService _manifestGeneratorService;
        private readonly ProductConsolidationService _productConsolidationService;
        private readonly AppConfigurationService _configService;
        private readonly ErrorHandlingService _errorHandler;
        private readonly ProductNameService _productNameService;

        // Reusable JSON serializer options
        private static readonly JsonSerializerOptions _jsonOptions = new JsonSerializerOptions
        {
            WriteIndented = true,
            PropertyNameCaseInsensitive = true
        };

        public Form1()
        {
            InitializeComponent();

            // Initialize configuration service
            _configService = new AppConfigurationService();

            // Initialize error handling service
            _errorHandler = new ErrorHandlingService();

            // Get the base directory of the application (go up from bin/Debug/etc.)
            string baseDir = AppDomain.CurrentDomain.BaseDirectory;
            // Go up three levels from the bin/Debug/net8.0-windows directory to the project root
            string projectDir = Path.GetFullPath(Path.Combine(baseDir, "..", "..", ".."));
            // Path to the mapping file in the Data folder
            string mappingFilePath = Path.Combine(projectDir, "Data", "Mapping.xlsx");

            // Initialize services
            _dataEnrichmentService = new DataEnrichmentService(mappingFilePath);
            _imageService = new ImageService();
            _manifestGeneratorService = new ManifestGeneratorService();
            _productConsolidationService = new ProductConsolidationService();
            _productNameService = new ProductNameService(mappingFilePath);

            // Initialize OpenAI service with API key from configuration and product name service
            _openAIService = new OpenAIService(_configService.Configuration.OpenAIApiKey, _productNameService);

            // Load product names for OpenAI API context
            var productNames = _productNameService.GetProductNames();
            Console.WriteLine($"Loaded {productNames.Count} product names from mapping file for OpenAI context");

            if (productNames.Count == 0)
            {
                Console.WriteLine("Warning: No product names found in mapping file. OpenAI will not have product name context.");
            }

            // Initialize with ready status
            UpdateStatus("Ready to analyze images");
        }


        private void btnUploadImage_Click(object sender, EventArgs e)
        {
            // Show the open file dialog
            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    // Get the selected file path
                    currentImagePath = openFileDialog.FileName;

                    // Load and display the image using the image service
                    var result = _imageService.LoadImage(currentImagePath);
                    pictureBox.Image = result.Image;

                    // Display image information
                    labelImageInfo.Text = _imageService.GetImageDescription(result.FileInfo);

                    // Enable the analyze button and reset its state
                    btnAnalyzeImage.Enabled = true;
                    btnAnalyzeImage.Text = "Analyze with ChatGPT";

                    // Clear previous results
                    textBoxResult.Clear();
                    dataGridView1.DataSource = null;
                    btnGenerateManifest.Enabled = false;
                    _currentEnrichedProducts = null;
                    _currentRawJson = string.Empty;
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error loading image: {ex.Message}", "Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
        private async void btnAnalyzeImage_Click(object sender, EventArgs e)
        {
            // Check if we're in "Process Edited Data" mode
            if (btnAnalyzeImage.Text == "Process Edited Data")
            {
                try
                {
                    btnAnalyzeImage.Enabled = false;
                    btnAnalyzeImage.Text = "Processing...";

                    await ProcessEditedData();
                }
                catch (Exception ex)
                {
                    HandleAnalysisError(ex);
                }
                finally
                {
                    btnAnalyzeImage.Enabled = true;
                }
                return;
            }

            // Original analysis flow
            if (!ValidateImagePath())
                return;

            try
            {
                SetUIProcessingState();

                await AnalyzeImageAndProcessResults();
            }
            catch (Exception ex)
            {
                HandleAnalysisError(ex);
            }
            finally
            {
                ResetUIAfterProcessing();
            }
        }

        /// <summary>
        /// Validates that an image path has been selected
        /// </summary>
        private bool ValidateImagePath()
        {
            if (string.IsNullOrEmpty(currentImagePath))
            {
                MessageBox.Show("Please upload an image first", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }
            return true;
        }

        /// <summary>
        /// Updates the UI to show processing state
        /// </summary>
        private void SetUIProcessingState()
        {
            btnAnalyzeImage.Enabled = false;
            btnAnalyzeImage.Text = "Processing...";
            UpdateStatus("Step 1/4: Preparing to analyze image...");
        }

        /// <summary>
        /// Resets the UI after processing is complete
        /// </summary>
        private void ResetUIAfterProcessing()
        {
            btnAnalyzeImage.Enabled = true;
            // Only reset to "Analyze with ChatGPT" if we're not in editing mode
            if (btnAnalyzeImage.Text != "Process Edited Data")
            {
                btnAnalyzeImage.Text = "Analyze with ChatGPT";
            }
        }

        /// <summary>
        /// Handles errors that occur during image analysis
        /// </summary>
        private void HandleAnalysisError(Exception ex)
        {
            MessageBox.Show($"Error: {ex.Message}", "Error",
                MessageBoxButtons.OK, MessageBoxIcon.Error);
            UpdateStatus($"Error occurred: {ex.Message}");
            Console.WriteLine($"Exception details: {ex}");
            textBoxResult.Text = $"Error: {ex.Message}\r\n\r\nStack Trace: {ex.StackTrace}";
        }

        /// <summary>
        /// Main method for analyzing the image and processing the results
        /// </summary>
        private async Task AnalyzeImageAndProcessResults()
        {
            // Extract text from image using ChatGPT
            UpdateStatus("Step 2/4: Sending image to ChatGPT for analysis...");

            // Use OpenAI API to analyze the image
            var (rawJson, products) = await _openAIService.AnalyzeImageAsync(currentImagePath);

            // For development/testing, you can use local data.json instead
            // string rawJson = File.ReadAllText("Data/data.json");
            // var products = JsonSerializer.Deserialize<List<ProductData>>(rawJson, _jsonOptionsWithConverter);

            // Debug information
            LogDebugInfo(rawJson, products);

            UpdateStatus("Step 3/4: Processing ChatGPT response...");
            await Task.Delay(500); // Brief delay for UI update

            // Always show the raw JSON for editing, regardless of parsing success
            // This allows users to review and fix any issues with the ChatGPT output
            if (!string.IsNullOrEmpty(rawJson) && !rawJson.StartsWith("Error") && !rawJson.StartsWith("API Error"))
            {
                ShowChatGPTOutputForEditing(rawJson);
            }
            else
            {
                HandleFailedAnalysis(rawJson);
            }
        }

        /// <summary>
        /// Logs debug information about the API response
        /// </summary>
        private static void LogDebugInfo(string rawJson, List<ProductData>? products)
        {
            Console.WriteLine($"Raw JSON received: {(string.IsNullOrEmpty(rawJson) ? "EMPTY" : rawJson.Length + " chars")}");
            Console.WriteLine($"Products parsed: {(products == null ? "NULL" : products.Count + " items")}");
        }

        /// <summary>
        /// Shows the ChatGPT output for manual editing before processing
        /// </summary>
        private void ShowChatGPTOutputForEditing(string rawJson)
        {
            // Store the raw JSON for later processing
            _currentRawJson = rawJson;

            // Display the raw JSON in the text box for editing
            if (string.IsNullOrEmpty(rawJson))
            {
                textBoxResult.Text = "No data received from ChatGPT. Please check your API key and try again.";
                UpdateStatus("Error: No data received from ChatGPT");

                MessageBox.Show(
                    "No data was received from ChatGPT.\n\n" +
                    "Possible causes:\n" +
                    "• Invalid or expired OpenAI API key\n" +
                    "• Network connectivity issues\n" +
                    "• OpenAI API service issues\n\n" +
                    "Please check your API key in the config.json file and try again.",
                    "No ChatGPT Response",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Warning);

                // Reset button state
                btnAnalyzeImage.Text = "Analyze with ChatGPT";
                btnAnalyzeImage.Enabled = true;
                return;
            }

            textBoxResult.Text = rawJson;

            // Update status to indicate editing phase
            UpdateStatus("Step 3/4: Review and edit ChatGPT output, then click 'Process Edited Data'");

            // Enable the "Process Edited Data" button and disable other buttons
            btnAnalyzeImage.Enabled = true;
            btnAnalyzeImage.Text = "Process Edited Data";
            btnGenerateManifest.Enabled = false;

            // Clear previous enriched data
            dataGridView1.DataSource = null;
            _currentEnrichedProducts = null;

            // Show a helpful message to the user
            MessageBox.Show(
                "ChatGPT analysis complete!\n\n" +
                "The raw JSON output is now displayed in the text area below.\n" +
                "You can review and edit it as needed, then click 'Process Edited Data' to continue.\n\n" +
                "Tips:\n" +
                "• Check product names for accuracy\n" +
                "• Verify quantities and dimensions\n" +
                "• Ensure JSON format is valid\n" +
                "• You can also upload a new image to start over",
                "Ready for Review & Edit",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information);

            Console.WriteLine("ChatGPT analysis complete. Waiting for user to review and edit the output.");
        }

        /// <summary>
        /// Processes the edited JSON data to create enriched product data
        /// </summary>
        private async Task ProcessEditedData()
        {
            try
            {
                // Get the edited JSON from the text box
                string editedJson = textBoxResult.Text;

                if (string.IsNullOrWhiteSpace(editedJson))
                {
                    MessageBox.Show("Please enter valid JSON data to process.", "No Data",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Validate JSON format before processing
                if (!IsValidJson(editedJson))
                {
                    var result = MessageBox.Show(
                        "The JSON format appears to be invalid. This may cause processing errors.\n\n" +
                        "Do you want to continue anyway?",
                        "JSON Validation Warning",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Warning);

                    if (result == DialogResult.No)
                    {
                        return;
                    }
                }

                UpdateStatus("Step 4/4: Processing edited data and enriching with mapping information...");

                // Use Task.Run for potentially CPU-intensive operation
                await Task.Run(() => {
                    _currentEnrichedProducts = _dataEnrichmentService.EnrichProductData(editedJson);
                });

                Console.WriteLine($"Enriched products from edited data: {(_currentEnrichedProducts == null ? "NULL" : _currentEnrichedProducts.Count + " items")}");

                // Update UI with results
                UpdateUIWithResults();

                // Reset button state
                btnAnalyzeImage.Text = "Analyze with ChatGPT";
                btnAnalyzeImage.Enabled = true;

                UpdateStatus($"Processing complete: Found {_currentEnrichedProducts?.Count ?? 0} product items");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error processing edited data: {ex.Message}", "Processing Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                UpdateStatus($"Error processing edited data: {ex.Message}");
                Console.WriteLine($"Error processing edited data: {ex}");
            }
        }

        /// <summary>
        /// Updates the UI with the analysis results
        /// </summary>
        private void UpdateUIWithResults()
        {
            // Clear previous datasource
            dataGridView1.DataSource = null;

            // Display the raw data in the JSON tab
            textBoxResult.Text = JsonSerializer.Serialize(_currentEnrichedProducts, _jsonOptions);

            // Update the data grid
            if (_currentEnrichedProducts != null && _currentEnrichedProducts.Count != 0)
            {
                dataGridView1.DataSource = _currentEnrichedProducts.ToList();
            }

            // Log the row count for debugging
            Console.WriteLine($"DataGridView has {dataGridView1.Rows.Count} rows");

            // Enable Generate Manifest button
            btnGenerateManifest.Enabled = _currentEnrichedProducts != null && _currentEnrichedProducts.Count > 0;
        }

        /// <summary>
        /// Handles a failed image analysis
        /// </summary>
        private void HandleFailedAnalysis(string rawJson)
        {
            // Show raw JSON if parsing failed
            UpdateStatus("Analysis complete: Failed to parse response into product data");
            textBoxResult.Text = string.IsNullOrEmpty(rawJson) ?
                "No valid response received from API" : rawJson;

            dataGridView1.DataSource = null;
            btnGenerateManifest.Enabled = false; // Disable Generate Manifest button
            _currentEnrichedProducts = null;

            if (!string.IsNullOrEmpty(rawJson) && rawJson.Contains("error"))
            {
                MessageBox.Show($"API Error: {rawJson}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void btnGenerateManifest_Click(object sender, EventArgs e)
        {
            if (!ValidateDataForManifest())
                return;

            try
            {
                string templatePath = GetTemplatePath();
                if (string.IsNullOrEmpty(templatePath))
                    return;

                ShowSaveFileDialog(templatePath);
            }
            catch (Exception ex)
            {
                _errorHandler.HandleException(ex, "Preparing manifest", UpdateStatus);
            }
        }

        /// <summary>
        /// Validates that we have data to generate a manifest
        /// </summary>
        private bool ValidateDataForManifest()
        {
            if (_currentEnrichedProducts == null || _currentEnrichedProducts.Count == 0)
            {
                MessageBox.Show("No data available to generate manifest.", "Manifest Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (string.IsNullOrEmpty(currentImagePath))
            {
                MessageBox.Show("Please upload an image first to locate the template file.", "Manifest Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        /// <summary>
        /// Gets the path to the template file
        /// </summary>
        private string GetTemplatePath()
        {
            // Get the directory where the image is located
            string imageDirectory = Path.GetDirectoryName(currentImagePath) ?? string.Empty;

            // Check if the template file exists in the same directory
            string templateFileName = _configService.Configuration.TemplateFileName;
            string templatePath = Path.Combine(imageDirectory, templateFileName);

            if (!File.Exists(templatePath))
            {
                MessageBox.Show($"Template file '{templateFileName}' not found in {imageDirectory}",
                    "Manifest Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return string.Empty;
            }

            return templatePath;
        }

        /// <summary>
        /// Shows the save file dialog and generates the manifest if a file is selected
        /// </summary>
        private void ShowSaveFileDialog(string templatePath)
        {
            // Get the directory where the image is located
            string imageDirectory = Path.GetDirectoryName(currentImagePath) ?? string.Empty;

            // Create a SaveFileDialog to choose the export location
            using SaveFileDialog saveFileDialog = new();
            saveFileDialog.Filter = "Excel Files|*.xlsx";
            saveFileDialog.Title = "Save Amazon India Shipment Manifest";
            saveFileDialog.FileName = "Amazon_India_Shipment_Manifest.xlsx";
            saveFileDialog.InitialDirectory = imageDirectory;

            if (saveFileDialog.ShowDialog() == DialogResult.OK)
            {
                GenerateManifestFile(templatePath, saveFileDialog.FileName);
            }
        }

        /// <summary>
        /// Generates the manifest file
        /// </summary>
        private void GenerateManifestFile(string templatePath, string outputPath)
        {
            try
            {
                UpdateStatus("Generating manifest file and consolidating duplicate SKUs...");

                // Validate that we have products to process
                if (_currentEnrichedProducts == null)
                {
                    throw new InvalidOperationException("No product data available");
                }

                // Consolidate products by SKU
                var consolidatedProducts = _productConsolidationService.CreateConsolidatedProductList(_currentEnrichedProducts);

                if (consolidatedProducts == null || consolidatedProducts.Count == 0)
                {
                    throw new InvalidOperationException("No valid products after consolidation");
                }

                // Generate the manifest
                _manifestGeneratorService.GenerateManifest(templatePath, outputPath, consolidatedProducts, UpdateStatus);

                MessageBox.Show("Amazon India Shipment Manifest generated successfully!", "Manifest Complete",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                UpdateStatus("Manifest generated successfully with consolidated SKUs");
            }
            catch (Exception ex)
            {
                _errorHandler.HandleException(ex, "Generating manifest", UpdateStatus, true);
            }
        }

        /// <summary>
        /// Validates if a string is valid JSON
        /// </summary>
        private bool IsValidJson(string jsonString)
        {
            try
            {
                JsonDocument.Parse(jsonString);
                return true;
            }
            catch (JsonException)
            {
                return false;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Updates the status message in the UI
        /// </summary>
        private void UpdateStatus(string message)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<string>(UpdateStatus), message);
                return;
            }

            labelStatus.Text = message;
            labelStatus.ForeColor = message.StartsWith("Error") ? SystemColor.Red : SystemColors.ControlText;

            // Refresh the label to ensure it's painted immediately
            labelStatus.Refresh();

            // Also log to console for debugging
            Console.WriteLine($"Status: {message}");

            // Force UI update
            Application.DoEvents();
        }
    }
}
